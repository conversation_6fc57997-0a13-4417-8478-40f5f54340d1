{"Version": 1, "WorkspaceRootPath": "D:\\solution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|d:\\solution\\prn231_su25_se170115.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|solutionrelative:prn231_su25_se170115.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{C2F6AA5D-4439-40D6-A9CD-28E50156C193}|BLL\\BLL.csproj|d:\\solution\\bll\\services\\leopardprofileservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C2F6AA5D-4439-40D6-A9CD-28E50156C193}|BLL\\BLL.csproj|solutionrelative:bll\\services\\leopardprofileservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|d:\\solution\\prn231_su25_se170115.api\\controllers\\leopardprofilescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|solutionrelative:prn231_su25_se170115.api\\controllers\\leopardprofilescontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|d:\\solution\\prn231_su25_se170115.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|solutionrelative:prn231_su25_se170115.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C2F6AA5D-4439-40D6-A9CD-28E50156C193}|BLL\\BLL.csproj|d:\\solution\\bll\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{C2F6AA5D-4439-40D6-A9CD-28E50156C193}|BLL\\BLL.csproj|solutionrelative:bll\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\uow\\genenicrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\uow\\genenicrepository.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\uow\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\uow\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\models\\loginmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\models\\loginmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\models\\listleopardprofilemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\models\\listleopardprofilemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\models\\errormap.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\models\\errormap.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\models\\createleopardprofilemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\models\\createleopardprofilemodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\models\\authresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\models\\authresponse.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\entities\\su25leoparddbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\entities\\su25leoparddbcontext.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\entities\\leopardtype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\entities\\leopardtype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|d:\\solution\\prn231_su25_se170115.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{07BB24BD-FDC0-4E26-A1DD-5948F287FC7F}|PRN231_SU25_SE170115.api\\PRN231_SU25_SE170115.api.csproj|solutionrelative:prn231_su25_se170115.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|d:\\solution\\dal\\entities\\leopardaccount.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{0F592A6C-7FDB-41A7-863B-C9AE6912AAF1}|DAL\\DAL.csproj|solutionrelative:dal\\entities\\leopardaccount.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 10, "Children": [{"$type": "Bookmark", "Name": "ST:11:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:2:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:3:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:4:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:12:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "LeopardProfilesController.cs", "DocumentMoniker": "D:\\solution\\PRN231_SU25_SE170115.api\\Controllers\\LeopardProfilesController.cs", "RelativeDocumentMoniker": "PRN231_SU25_SE170115.api\\Controllers\\LeopardProfilesController.cs", "ToolTip": "D:\\solution\\PRN231_SU25_SE170115.api\\Controllers\\LeopardProfilesController.cs", "RelativeToolTip": "PRN231_SU25_SE170115.api\\Controllers\\LeopardProfilesController.cs", "ViewState": "AQIAABsAAAAAAAAAAAAYwCgAAAAbAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:08:36.625Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "GenenicRepository.cs", "DocumentMoniker": "D:\\solution\\DAL\\UOW\\GenenicRepository.cs", "RelativeDocumentMoniker": "DAL\\UOW\\GenenicRepository.cs", "ToolTip": "D:\\solution\\DAL\\UOW\\GenenicRepository.cs", "RelativeToolTip": "DAL\\UOW\\GenenicRepository.cs", "ViewState": "AQIAABcAAAAAAAAAAAAgwCIAAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:11:59.635Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AuthController.cs", "DocumentMoniker": "D:\\solution\\PRN231_SU25_SE170115.api\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "PRN231_SU25_SE170115.api\\Controllers\\AuthController.cs", "ToolTip": "D:\\solution\\PRN231_SU25_SE170115.api\\Controllers\\AuthController.cs", "RelativeToolTip": "PRN231_SU25_SE170115.api\\Controllers\\AuthController.cs", "ViewState": "AQIAAAYAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:12:03.773Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ListLeopardProfileModel.cs", "DocumentMoniker": "D:\\solution\\DAL\\Models\\ListLeopardProfileModel.cs", "RelativeDocumentMoniker": "DAL\\Models\\ListLeopardProfileModel.cs", "ToolTip": "D:\\solution\\DAL\\Models\\ListLeopardProfileModel.cs", "RelativeToolTip": "DAL\\Models\\ListLeopardProfileModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:27:39.227Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "AuthService.cs", "DocumentMoniker": "D:\\solution\\BLL\\Services\\AuthService.cs", "RelativeDocumentMoniker": "BLL\\Services\\AuthService.cs", "ToolTip": "D:\\solution\\BLL\\Services\\AuthService.cs", "RelativeToolTip": "BLL\\Services\\AuthService.cs", "ViewState": "AQIAAD4AAAAAAAAAAAAgwAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:11:26.097Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "LoginModel.cs", "DocumentMoniker": "D:\\solution\\DAL\\Models\\LoginModel.cs", "RelativeDocumentMoniker": "DAL\\Models\\LoginModel.cs", "ToolTip": "D:\\solution\\DAL\\Models\\LoginModel.cs", "RelativeToolTip": "DAL\\Models\\LoginModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:27:43.362Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "LeopardProfileService.cs", "DocumentMoniker": "D:\\solution\\BLL\\Services\\LeopardProfileService.cs", "RelativeDocumentMoniker": "BLL\\Services\\LeopardProfileService.cs", "ToolTip": "D:\\solution\\BLL\\Services\\LeopardProfileService.cs", "RelativeToolTip": "BLL\\Services\\LeopardProfileService.cs", "ViewState": "AQIAAAkAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:11:37.932Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "UnitOfWork.cs", "DocumentMoniker": "D:\\solution\\DAL\\UOW\\UnitOfWork.cs", "RelativeDocumentMoniker": "DAL\\UOW\\UnitOfWork.cs", "ToolTip": "D:\\solution\\DAL\\UOW\\UnitOfWork.cs", "RelativeToolTip": "DAL\\UOW\\UnitOfWork.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:12:00.975Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "D:\\solution\\PRN231_SU25_SE170115.api\\appsettings.json", "RelativeDocumentMoniker": "PRN231_SU25_SE170115.api\\appsettings.json", "ToolTip": "D:\\solution\\PRN231_SU25_SE170115.api\\appsettings.json", "RelativeToolTip": "PRN231_SU25_SE170115.api\\appsettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-20T03:31:17.893Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "ErrorMap.cs", "DocumentMoniker": "D:\\solution\\DAL\\Models\\ErrorMap.cs", "RelativeDocumentMoniker": "DAL\\Models\\ErrorMap.cs", "ToolTip": "D:\\solution\\DAL\\Models\\ErrorMap.cs", "RelativeToolTip": "DAL\\Models\\ErrorMap.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABUAAAAFAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:17:00.38Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Su25leopardDbContext.cs", "DocumentMoniker": "D:\\solution\\DAL\\Entities\\Su25leopardDbContext.cs", "RelativeDocumentMoniker": "DAL\\Entities\\Su25leopardDbContext.cs", "ToolTip": "D:\\solution\\DAL\\Entities\\Su25leopardDbContext.cs", "RelativeToolTip": "DAL\\Entities\\Su25leopardDbContext.cs", "ViewState": "AQIAACkAAAAAAAAAAAAswAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:27:12.115Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "AuthResponse.cs", "DocumentMoniker": "D:\\solution\\DAL\\Models\\AuthResponse.cs", "RelativeDocumentMoniker": "DAL\\Models\\AuthResponse.cs", "ToolTip": "D:\\solution\\DAL\\Models\\AuthResponse.cs", "RelativeToolTip": "DAL\\Models\\AuthResponse.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:11:54.937Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "CreateLeopardProfileModel.cs", "DocumentMoniker": "D:\\solution\\DAL\\Models\\CreateLeopardProfileModel.cs", "RelativeDocumentMoniker": "DAL\\Models\\CreateLeopardProfileModel.cs", "ToolTip": "D:\\solution\\DAL\\Models\\CreateLeopardProfileModel.cs", "RelativeToolTip": "DAL\\Models\\CreateLeopardProfileModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:08:23.964Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "LeopardType.cs", "DocumentMoniker": "D:\\solution\\DAL\\Entities\\LeopardType.cs", "RelativeDocumentMoniker": "DAL\\Entities\\LeopardType.cs", "ToolTip": "D:\\solution\\DAL\\Entities\\LeopardType.cs", "RelativeToolTip": "DAL\\Entities\\LeopardType.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:27:11.592Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "Program.cs", "DocumentMoniker": "D:\\solution\\PRN231_SU25_SE170115.api\\Program.cs", "RelativeDocumentMoniker": "PRN231_SU25_SE170115.api\\Program.cs", "ToolTip": "D:\\solution\\PRN231_SU25_SE170115.api\\Program.cs", "RelativeToolTip": "PRN231_SU25_SE170115.api\\Program.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:12:08.921Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "LeopardAccount.cs", "DocumentMoniker": "D:\\solution\\DAL\\Entities\\LeopardAccount.cs", "RelativeDocumentMoniker": "DAL\\Entities\\LeopardAccount.cs", "ToolTip": "D:\\solution\\DAL\\Entities\\LeopardAccount.cs", "RelativeToolTip": "DAL\\Entities\\LeopardAccount.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T03:11:53.453Z"}]}]}]}