{"info": {"_postman_id": "a607580b-f24c-4d95-a0e0-2cf25a0fba92", "name": "PRN231_SU25_SE170115", "description": "Comprehensive test collection for Handbag API with JWT authentication - Optimized with variables", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "36442572"}, "item": [{"name": "1. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200 OK\", () => {", "    pm.response.to.have.status(200);", "});", "", "const res = pm.response.json();", "", "pm.expect(res).to.have.property(\"token\");", "pm.expect(res).to.have.property(\"role\");", "", "pm.environment.set(\"token\", res.token);", "pm.environment.set(\"user_role\", res.role);", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiNDg0Y2QwYWYtZjVjOS00MmZhLWExNmUtMjJkMzMzNWFmZmY4IiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2Nzc5NywiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.NZR91wlB5VJvEIjT-of2vgePhH_b31tuVhmT3rXgCEI", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"@1\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}, "response": []}, {"name": "2. <PERSON><PERSON> Failed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 401 Unauthorized\", () => {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsImp0aSI6ImE5NWZiYzY3LWViZWUtNGY2MC1hOGJlLWMwNzhiYWY1MmVkMCIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6ImFkbWluaXN0cmF0b3IiLCJleHAiOjE3NTI5MDUyODQsImlzcyI6InBlcHJuMjMyIiwiYXVkIjoicGVwcm4yMzIifQ.*******************************************", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"@1\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}, "response": []}, {"name": "3. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 201 Created\", () => {", "    pm.response.to.have.status(201);", "});", "", "const res = pm.response.json();", "", "const newId = res.item.handbagId || res.item.id;", "if (newId) {", "    pm.environment.set(\"id_to_update\", newId);", "    pm.environment.set(\"id_to_delete\", newId);", "}", "", "const authHeader = pm.request.headers.get(\"Authorization\");", "const token = pm.environment.get(\"token\");", "", "pm.expect(token, \"Token not set\").to.not.be.undefined;", "", "if (authHeader) {", "    pm.expect(authHeader).to.include(\"Bearer\");", "}", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiMzE4MTIxNzYtMDE0Yi00NTg3LWJlMTQtOTFjYzRmMDdmMmRiIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2ODA2MiwiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.kuL-HOl6dv4PndUELMOaWQb6Dju12mFKX_2v0VvhoJw", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"leopardProfileId\": 0,\n  \"leopardTypeId\": 1,\n  \"leopardName\": \"Panthera TS\",\n  \"weight\": 35,\n  \"characteristics\": \"The leopard possesses\",\n  \"careNeeds\": \"These animals are classified\",\n  \"modifiedDate\": \"2025-07-20T03:13:12.418Z\"\n}"}, "url": {"raw": "{{base_url}}/api/LeopardProfile", "host": ["{{base_url}}"], "path": ["api", "LeopardProfile"]}}, "response": []}, {"name": "4. Update <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200 OK\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "const authHeader = pm.request.headers.get(\"Authorization\");\r", "const token = pm.environment.get(\"token\");\r", "\r", "pm.expect(token, \"Token not set\").to.not.be.undefined;\r", "\r", "if (authHeader) {\r", "    pm.expect(authHeader).to.include(\"Bearer\");\r", "} \r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiMzE4MTIxNzYtMDE0Yi00NTg3LWJlMTQtOTFjYzRmMDdmMmRiIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2ODA2MiwiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.kuL-HOl6dv4PndUELMOaWQb6Dju12mFKX_2v0VvhoJw", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"leopardProfileId\": 0,\n  \"leopardTypeId\": 1,\n  \"leopardName\": \"Panthera Tqq\",\n  \"weight\": 35,\n  \"characteristics\": \"The leopard possesses\",\n  \"careNeeds\": \"These animals are classified\",\n  \"modifiedDate\": \"2025-07-20T03:13:12.418Z\"\n}"}, "url": {"raw": "{{base_url}}/api/LeopardProfile/6", "host": ["{{base_url}}"], "path": ["api", "LeopardProfile", "6"]}}, "response": []}, {"name": "5. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200 OK\", () => {", "    pm.response.to.have.status(200);", "});", "", "const authHeader = pm.request.headers.get(\"Authorization\");", "const token = pm.environment.get(\"token\");", "", "pm.expect(token, \"Token not set\").to.not.be.undefined;", "", "if (authHeader) {", "    pm.expect(authHeader).to.include(\"Bearer\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiMzE4MTIxNzYtMDE0Yi00NTg3LWJlMTQtOTFjYzRmMDdmMmRiIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2ODA2MiwiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.kuL-HOl6dv4PndUELMOaWQb6Dju12mFKX_2v0VvhoJw", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "file", "file": {}}, "url": {"raw": "{{base_url}}/api/LeopardProfile/10", "host": ["{{base_url}}"], "path": ["api", "LeopardProfile", "10"]}}, "response": []}, {"name": "6a. Get All Leopard", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200 OK\", () => {", "    pm.response.to.have.status(200);", "});", "", "const res = pm.response.json();", "if (res.length > 0) {", "    const id = res[0].handbagId || res[0].id;", "    pm.environment.set(\"id\", id);", "} ", "", "const authHeader = pm.request.headers.get(\"Authorization\");", "const token = pm.environment.get(\"token\");", "", "pm.expect(token, \"Token not set\").to.not.be.undefined;", "", "if (authHeader) {", "    pm.expect(authHeader).to.include(\"Bearer\");", "}", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiMzE4MTIxNzYtMDE0Yi00NTg3LWJlMTQtOTFjYzRmMDdmMmRiIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2ODA2MiwiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.kuL-HOl6dv4PndUELMOaWQb6Dju12mFKX_2v0VvhoJw", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/LeopardProfile", "host": ["{{base_url}}"], "path": ["api", "LeopardProfile"]}}, "response": []}, {"name": "6b. <PERSON> by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status is 200 OK\", () => {", "    pm.response.to.have.status(200);", "});", "", "const res = pm.response.json();", "pm.expect(res).to.have.property(\"modelName\");", "", "const authHeader = pm.request.headers.get(\"Authorization\");", "const token = pm.environment.get(\"token\");", "", "pm.expect(token, \"Token not set\").to.not.be.undefined;", "", "if (authHeader) {", "    pm.expect(authHeader).to.include(\"Bearer\");", "}"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbmlzdHJhdG9yIiwianRpIjoiNDg0Y2QwYWYtZjVjOS00MmZhLWExNmUtMjJkMzMzNWFmZmY4IiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoiYWRtaW5pc3RyYXRvciIsImV4cCI6MTc1MzA2Nzc5NywiaXNzIjoicGVwcm4yMzIiLCJhdWQiOiJwZXBybjIzMiJ9.NZR91wlB5VJvEIjT-of2vgePhH_b31tuVhmT3rXgCEI", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/LeopardProfile/5", "host": ["{{base_url}}"], "path": ["api", "LeopardProfile", "5"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'https://localhost:7120');", "}", "", "const requiredVars = ['token', 'user_role', 'id', 'id_to_update', 'id_to_delete'];", "requiredVars.forEach(key => {", "    if (!pm.environment.get(key)) {", "        console.warn(`Not set`);", "    }", "});", "", "const requestName = pm.info.requestName || '';", "const needsAuth = !requestName.toLowerCase().includes('login') && !requestName.toLowerCase().includes('without token');", "", "if (needsAuth) {", "    const token = pm.environment.get('token');", "}", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}